<script setup lang="jsx">
import { ref } from 'vue'
import ContentWrap from '../components/ContentWrap.vue'
import LineChart from '../components/LineChart.vue'

const form = ref({
  time: [],
  field: '',
  interface: '',
  application: '',
})

const chartData = ref({
  xAxisData: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  seriesData: [150, 230, 224, 218, 135, 147, 260],
})

const fieldData = ref([
  {
    field: '字段1',
    count: 100,
  },
  {
    field: '字段2',
    count: 200,
  },
  {
    field: '字段3',
    count: 300,
  },
  {
    field: '字段4',
    count: 400,
  },
  {
    field: '字段5',
    count: 500,
  },
  {
    field: '字段6',
    count: 600,
  },
  {
    field: '字段7',
    count: 700,
  },
  {
    field: '字段8',
    count: 800,
  },
  {
    field: '字段9',
    count: 900,
  },
])

const interfaceData = ref([
  {
    interfaceCode: '接口1',
    count: 100,
  },
  {
    interfaceCode: '接口2',
    count: 200,
  },
  {
    interfaceCode: '接口3',
    count: 300,
  },
])

const applicationData = ref([
  {
    application: '应用1',
    count: 100,
  },
  {
    application: '应用2',
    count: 200,
  },
  {
    application: '应用3',
    count: 300,
  },
])

const fieldColumns = [
  {
    title: '字段名',
    colKey: 'field',
  },
  {
    title: '调用次数',
    colKey: 'count',
  },
]

const interfaceColumns = [
  {
    title: '接口编码',
    colKey: 'interfaceCode',
  },
  {
    title: '调用次数',
    colKey: 'count',
  },
]

const applicationColumns = [
  {
    title: '应用编码',
    colKey: 'application',
  },
  {
    title: '调用次数',
    colKey: 'count',
  },
]
</script>

<template>
  <div class="usage-situation-page">
    <div class="filter-form">
      <t-form :data="form" layout="inline" labelAlign="left" labelWidth="10">
        <t-form-item label="日期">
          <t-date-range-picker
            v-model="form.time"
            enable-time-picker
            allow-input
            clearable
            style="width: 366px"
            :placeholder="['开始时间', '结束时间']"
          />
        </t-form-item>
        <t-form-item label="字段">
          <t-input v-model="form.field"></t-input>
        </t-form-item>
        <t-form-item label="接口">
          <t-input v-model="form.interface"></t-input>
        </t-form-item>
        <t-form-item label="应用">
          <t-select v-model="form.application" :options="[]"></t-select>
        </t-form-item>
        <t-form-item>
          <t-space>
            <t-button variant="outline">查询</t-button>
            <t-button variant="outline">重置</t-button>
          </t-space>
        </t-form-item>
      </t-form>
    </div>
    <t-row :gutter="[20, 20]">
      <!-- 第一行，3列 -->
      <t-col :span="4">
        <div class="wrap">
          <ContentWrap title="请求量">
            <LineChart :chartData="chartData"></LineChart>
          </ContentWrap>
        </div>
      </t-col>
      <t-col :span="4">
        <div class="wrap">
          <ContentWrap title="字段使用排名">
            <t-table
              row-key="id"
              :columns="fieldColumns"
              :data="fieldData"
              style="height: 100%"
              max-height="100%"
            ></t-table>
          </ContentWrap>
        </div>
      </t-col>
      <t-col :span="4">
        <div class="wrap">
          <ContentWrap title="接口调用排名">
            <t-table
              row-key="id"
              :columns="interfaceColumns"
              :data="interfaceData"
              style="height: 100%"
              max-height="100%"
            ></t-table>
          </ContentWrap>
        </div>
      </t-col>
    </t-row>

    <t-row :gutter="[20, 20]">
      <!-- 第二行，1列，宽度与上面每列相同 -->
      <t-col :span="4">
        <div class="wrap">
          <ContentWrap title="应用调用排名">
            <t-table
              row-key="id"
              :columns="applicationColumns"
              :data="applicationData"
              style="height: 100%"
              max-height="100%"
            ></t-table>
          </ContentWrap>
        </div>
      </t-col>
    </t-row>
  </div>
</template>

<style lang="less" scoped>
.usage-situation-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  .filter-form {
    margin-bottom: 20px;
    :deep(.t-form__item) {
      &:not(.t-form__item-with-extra) {
        margin-bottom: 0;
      }
    }
  }
  :deep(.t-row) {
    height: 360px;
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .t-col {
      height: 100%;
    }
    .wrap {
      height: 100%;
      border-radius: 4px;
      border: 1px solid #edeff7;
      padding: var(--trace-lineage-padding);
    }
  }
  :deep(.t-table th) {
    background: #fff;
    color: #999999;
  }
  :deep(.t-table__header--fixed:not(.t-table__header--multiple) > tr > th) {
    background: #fff;
    color: #999999;
  }
}
</style>

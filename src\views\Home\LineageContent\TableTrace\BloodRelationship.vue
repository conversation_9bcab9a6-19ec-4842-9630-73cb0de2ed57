<script setup lang="jsx">
import { onMounted, onUnmounted, ref } from 'vue'
import { RefreshIcon, SearchIcon } from 'tdesign-icons-vue-next'
import { useG6Graph } from '@/hooks/useG6Graph'
import { treeToGraphData } from '@antv/g6'
import emitter, { EVENT_NAMES } from '@/utils/emitter'
import JobNode from '@/components/JobNode.vue'

const data = {
  id: 'Modeling Methods111111111111111111111111',
  data: {
    type: 'table',
  },
  children: [
    {
      id: 'Classification',
      children: [
        {
          id: 'Logistic regression',
        },
        {
          id: 'Linear discriminant analysis',
        },
        {
          id: 'Rules',
        },
        {
          id: 'Decision trees',
        },
        {
          id: 'Naive Bayes',
        },
        {
          id: 'K nearest neighbor',
        },
        {
          id: 'Probabilistic neural network',
        },
        {
          id: 'Support vector machine',
        },
      ],
    },
    {
      id: 'Consensus',
      children: [
        {
          id: 'Models diversity',
          children: [
            {
              id: 'Different initializations',
            },
            {
              id: 'Different parameter choices',
            },
            {
              id: 'Different architectures',
            },
            {
              id: 'Different modeling methods',
            },
            {
              id: 'Different training sets',
            },
            {
              id: 'Different feature sets',
            },
          ],
        },
        {
          id: 'Methods',
          children: [
            {
              id: 'Classifier selection',
            },
            {
              id: 'Classifier fusion',
            },
          ],
        },
        {
          id: 'Common',
          children: [
            {
              id: 'Bagging',
            },
            {
              id: 'Boosting',
            },
            {
              id: 'AdaBoost',
            },
          ],
        },
      ],
    },
    {
      id: 'Regression',
      children: [
        {
          id: 'Multiple linear regression',
        },
        {
          id: 'Partial least squares',
        },
        {
          id: 'Multi-layer feedforward neural network',
        },
        {
          id: 'General regression neural network',
        },
        {
          id: 'Support vector regression',
        },
      ],
    },
  ],
}
// 使用组合式函数
const { paintboardRef } = useG6Graph(treeToGraphData(data), {
  node: {
    type: 'job-node',
    style: {
      component: (data) => <JobNode data={Object.assign({}, data)} />,
    },
  },
})

const visible = ref(false)
const currentNodeType = ref('')
const nodeCompMapping = {
  // datasource: DatasourceDetail,
  // table: TableDetail,
  // field: FieldDetail,
  // interface: InterfaceDetail,
  // application: ApplicationDetail,
  // model: ModelDetail,
}
const nodeTitleMapping = {
  datasource: '数据源详情',
  table: '表详情',
  field: '字段详情',
  interface: '接口详情',
  application: '应用详情',
  model: '数据模型详情',
}
const refMap = {}
function setRefMap(el, current) {
  if (el) {
    refMap[current] = el
  }
}

function handleNodeClick(data) {
  console.log('[ data ] >', data)
  // visible.value = true
}

onMounted(() => {
  emitter.on(EVENT_NAMES.NODE_CLICK, handleNodeClick)
})

onUnmounted(() => {
  emitter.off(EVENT_NAMES.NODE_CLICK, handleNodeClick)
})
</script>

<template>
  <div class="blood-relationship-page">
    <div class="paintboard-tool-left">
      <t-space size="10px">
        <t-input placeholder="应用">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-input placeholder="数据表">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
        <t-input placeholder="字段">
          <template #suffixIcon>
            <search-icon :style="{ cursor: 'pointer' }" />
          </template>
        </t-input>
      </t-space>
    </div>
    <div class="paintboard-tool-right">
      <span class="paintboard-tool-right-wrap">
        <RefreshIcon />
      </span>
    </div>
    <div ref="paintboardRef" class="paintboard-inner"></div>
  </div>
  <t-drawer v-model:visible="visible" :closeBtn="true" size="1056px" destroyOnClose>
    <template #header>{{ nodeTitleMapping[currentNodeType] }}</template>
    <component
      :is="nodeCompMapping[currentNodeType]"
      :ref="(el) => setRefMap(el, currentNodeType)"
    ></component>
  </t-drawer>
</template>

<style lang="less" scoped>
.blood-relationship-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  background-color: #eee;
  position: relative;
  .paintboard-tool-left,
  .paintboard-tool-right {
    position: absolute;
    top: 10px;
    height: 32px;
    line-height: 32px;
    z-index: 1001;
  }
  .paintboard-tool-left {
    left: 10px;
  }
  .paintboard-tool-right {
    right: 10px;
    .paintboard-tool-right-wrap {
      height: 32px;
      width: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      background-color: #fff;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  .paintboard-inner {
    height: 100%;
    width: 100%;
  }
}
</style>

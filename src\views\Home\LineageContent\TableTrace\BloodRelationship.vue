<script setup lang="jsx">
import { Graph, treeToGraphData } from '@antv/g6'
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { registerJobNode } from '@/utils/graph'
import JobNode from '@/components/JobNode.vue'

registerJobNode()

let graph = null // 不使用ref包装，避免响应式冲突
const paintboardRef = ref(null)
let isInitializing = false // 防止重复初始化

const data = {
  id: 'Modeling Methods111111111111111111111111',
  data: {
    type: 'table',
  },
  children: [
    {
      id: 'Classification',
      children: [
        {
          id: 'Logistic regression',
        },
        {
          id: 'Linear discriminant analysis',
        },
        {
          id: 'Rules',
        },
        {
          id: 'Decision trees',
        },
        {
          id: 'Naive Bayes',
        },
        {
          id: 'K nearest neighbor',
        },
        {
          id: 'Probabilistic neural network',
        },
        {
          id: 'Support vector machine',
        },
      ],
    },
    {
      id: 'Consensus',
      children: [
        {
          id: 'Models diversity',
          children: [
            {
              id: 'Different initializations',
            },
            {
              id: 'Different parameter choices',
            },
            {
              id: 'Different architectures',
            },
            {
              id: 'Different modeling methods',
            },
            {
              id: 'Different training sets',
            },
            {
              id: 'Different feature sets',
            },
          ],
        },
        {
          id: 'Methods',
          children: [
            {
              id: 'Classifier selection',
            },
            {
              id: 'Classifier fusion',
            },
          ],
        },
        {
          id: 'Common',
          children: [
            {
              id: 'Bagging',
            },
            {
              id: 'Boosting',
            },
            {
              id: 'AdaBoost',
            },
          ],
        },
      ],
    },
    {
      id: 'Regression',
      children: [
        {
          id: 'Multiple linear regression',
        },
        {
          id: 'Partial least squares',
        },
        {
          id: 'Multi-layer feedforward neural network',
        },
        {
          id: 'General regression neural network',
        },
        {
          id: 'Support vector regression',
        },
      ],
    },
  ],
}

async function initPaintboard() {
  // 防止重复初始化
  if (isInitializing) {
    return
  }

  isInitializing = true

  try {
    // 如果已存在graph实例，先销毁
    if (graph) {
      graph.destroy()
      graph = null
    }

    // 创建新的G6实例
    graph = new Graph({
      container: paintboardRef.value,
      padding: 20,
      autoResize: true,
      autoFit: {
        type: 'view', // 自适应类型：'view' 或 'center'
      },
      background: '#eeefef',
      data: treeToGraphData(data),
      node: {
        type: 'job-node',
        style: {
          component: (data) => <JobNode data={Object.assign({}, data)} />,
        },
      },
      edge: {
        type: 'cubic-horizontal',
        animation: {
          enter: false,
        },
      },
      layout: {
        type: 'dendrogram',
        direction: 'LR', // H / V / LR / RL / TB / BT
        nodeSep: 80, // 40 + 40
        rankSep: 300, // 240 + 60
      },
      behaviors: [
        'drag-canvas',
        'zoom-canvas',
        {
          type: 'drag-element',
          enable: true,
          // 添加配置来避免事件冲突
          eventName: 'drag',
        },
        'collapse-expand',
        'hover-activate', // 节点hover
        'click-select', // 节点click
      ],
    })

    graph.render()
  } catch (error) {
    console.error('Failed to initialize G6 graph:', error)
  } finally {
    isInitializing = false
  }
}

// 清理函数
function destroyGraph() {
  if (graph) {
    try {
      graph.destroy()
      graph = null
    } catch (error) {
      console.error('Error destroying graph:', error)
    }
  }
}

onMounted(() => {
  initPaintboard()
})

// 组件销毁时清理G6实例
onBeforeUnmount(() => {
  destroyGraph()
})
</script>

<template>
  <div class="blood-relationship-page">
    <div ref="paintboardRef" class="paintboard"></div>
  </div>
</template>

<style lang="less" scoped>
.blood-relationship-page {
  margin-top: 20px;
  height: calc(100% - 20px);
  background-color: #eee;
  .paintboard {
    height: 100%;
  }
}
</style>

import { ref, onMounted, onBeforeUnmount } from 'vue'
import { Graph } from '@antv/g6'
import { registerJobNode } from '@/utils/graph'

// 注册自定义节点
registerJobNode()

export function useG6Graph(payload, options = {}) {
  const paintboardRef = ref(null)
  let graph = null
  let isInitializing = false

  // 默认配置
  const defaultOptions = {
    padding: 20,
    autoResize: true,
    autoFit: {
      type: 'view',
    },
    background: '#eeefef',
    node: { ...options.node },
    edge: {
      type: 'cubic-horizontal',
      animation: {
        enter: false,
      },
    },
    layout: {
      type: 'dendrogram',
      direction: 'LR', // H / V / LR / RL / TB / BT
      nodeSep: 80, // 40 + 40
      rankSep: 300, // 240 + 60
    },
    behaviors: [
      'drag-canvas',
      'zoom-canvas',
      {
        type: 'drag-element',
        enable: true,
        // 添加配置来避免事件冲突
        eventName: 'drag',
      },
      'collapse-expand',
      'hover-activate', // 节点hover
      'click-select', // 节点click
    ],
    ...options,
  }

  const initGraph = async () => {
    // 防止重复初始化
    if (isInitializing) return
    isInitializing = true

    try {
      if (graph) {
        graph.destroy()
        graph = null
      }

      graph = new Graph({
        container: paintboardRef.value,
        ...defaultOptions,
        data: payload,
      })

      await graph.render()
    } catch (error) {
      console.error('Failed to initialize G6 graph:', error)
    } finally {
      isInitializing = false
    }
  }

  const destroyGraph = () => {
    if (graph) {
      graph.destroy()
      graph = null
    }
  }

  // 自动在组件挂载和卸载时调用
  onMounted(() => {
    initGraph()
  })

  onBeforeUnmount(() => {
    destroyGraph()
  })

  return {
    paintboardRef,
    graph, // 可选：暴露 graph 实例用于外部操作
    initGraph,
    destroyGraph,
  }
}
